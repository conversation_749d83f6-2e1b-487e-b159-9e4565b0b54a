import { Dimensions } from 'react-native';
import { useState, useEffect } from 'react';
import { breakpoints, fonts, spacing } from '../styles/theme';

// Get current screen dimensions
export const getScreenDimensions = () => {
  const { width, height } = Dimensions.get('window');
  return { width, height };
};

// Determine screen size category
export const getScreenSize = (width = null) => {
  const screenWidth = width || getScreenDimensions().width;
  
  if (screenWidth <= breakpoints.small) {
    return 'small';
  } else if (screenWidth <= breakpoints.medium) {
    return 'medium';
  } else if (screenWidth <= breakpoints.large) {
    return 'large';
  } else if (screenWidth <= breakpoints.xlarge) {
    return 'xlarge';
  } else {
    return 'xxlarge';
  }
};

// Hook to get responsive screen size
export const useResponsiveScreen = () => {
  const [screenData, setScreenData] = useState(() => {
    const dimensions = getScreenDimensions();
    return {
      ...dimensions,
      size: getScreenSize(dimensions.width),
    };
  });

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setScreenData({
        width: window.width,
        height: window.height,
        size: getScreenSize(window.width),
      });
    });

    return () => subscription?.remove();
  }, []);

  return screenData;
};

// Get responsive font size
export const getResponsiveFontSize = (size, screenSize = null) => {
  const currentScreenSize = screenSize || getScreenSize();
  return fonts.sizes[size]?.[currentScreenSize] || fonts.sizes[size]?.medium || 16;
};

// Get responsive spacing
export const getResponsiveSpacing = (size, screenSize = null) => {
  const currentScreenSize = screenSize || getScreenSize();
  return spacing[size]?.[currentScreenSize] || spacing[size]?.medium || 16;
};

// Get responsive dimensions for common UI elements
export const getResponsiveDimensions = (screenSize = null) => {
  const currentScreenSize = screenSize || getScreenSize();
  
  const dimensions = {
    small: {
      tabBarHeight: 50,
      headerHeight: 50,
      buttonHeight: 36,
      buttonMinWidth: 80,
      iconSize: 20,
      avatarSize: 32,
      cardPadding: 8,
      borderRadius: 6,
    },
    medium: {
      tabBarHeight: 55,
      headerHeight: 55,
      buttonHeight: 40,
      buttonMinWidth: 90,
      iconSize: 22,
      avatarSize: 36,
      cardPadding: 12,
      borderRadius: 8,
    },
    large: {
      tabBarHeight: 60,
      headerHeight: 60,
      buttonHeight: 44,
      buttonMinWidth: 100,
      iconSize: 24,
      avatarSize: 40,
      cardPadding: 16,
      borderRadius: 8,
    },
    xlarge: {
      tabBarHeight: 65,
      headerHeight: 65,
      buttonHeight: 48,
      buttonMinWidth: 110,
      iconSize: 26,
      avatarSize: 44,
      cardPadding: 20,
      borderRadius: 10,
    },
    xxlarge: {
      tabBarHeight: 70,
      headerHeight: 70,
      buttonHeight: 52,
      buttonMinWidth: 120,
      iconSize: 28,
      avatarSize: 48,
      cardPadding: 24,
      borderRadius: 12,
    },
  };

  return dimensions[currentScreenSize] || dimensions.medium;
};

// Check if screen is small (for conditional rendering)
export const isSmallScreen = (screenSize = null) => {
  const currentScreenSize = screenSize || getScreenSize();
  return currentScreenSize === 'small';
};

// Check if screen is large (tablet size)
export const isLargeScreen = (screenSize = null) => {
  const currentScreenSize = screenSize || getScreenSize();
  return currentScreenSize === 'xlarge' || currentScreenSize === 'xxlarge';
};

// Get responsive grid columns
export const getResponsiveColumns = (screenSize = null) => {
  const currentScreenSize = screenSize || getScreenSize();

  const columns = {
    small: 2,
    medium: 2,
    large: 2,
    xlarge: 2,
    xxlarge: 3,
  };

  return columns[currentScreenSize] || 2;
};

// Get responsive meal card width
export const getResponsiveMealCardWidth = (screenSize = null) => {
  const currentScreenSize = screenSize || getScreenSize();
  const { width } = getScreenDimensions();
  const columns = getResponsiveColumns(currentScreenSize);

  // Calculate card width based on screen width and number of columns
  // Account for padding and margins
  const totalPadding = 32; // 16px on each side
  const cardMargin = 8; // 4px on each side per card
  const availableWidth = width - totalPadding;
  const cardWidth = (availableWidth - (cardMargin * columns * 2)) / columns;

  return Math.floor(cardWidth);
};

// Get responsive card width for horizontal lists
export const getResponsiveCardWidth = (screenSize = null) => {
  const currentScreenSize = screenSize || getScreenSize();
  const { width } = getScreenDimensions();
  
  const cardWidths = {
    small: width * 0.7,   // 70% of screen width
    medium: width * 0.65, // 65% of screen width
    large: width * 0.6,   // 60% of screen width
    xlarge: width * 0.4,  // 40% of screen width
    xxlarge: width * 0.3, // 30% of screen width
  };

  return cardWidths[currentScreenSize] || width * 0.65;
};

// Get responsive safe area padding
export const getResponsiveSafeArea = (screenSize = null) => {
  const currentScreenSize = screenSize || getScreenSize();
  
  const safeArea = {
    small: {
      top: 20,
      bottom: 10,
      horizontal: 10,
    },
    medium: {
      top: 25,
      bottom: 15,
      horizontal: 15,
    },
    large: {
      top: 30,
      bottom: 20,
      horizontal: 20,
    },
    xlarge: {
      top: 35,
      bottom: 25,
      horizontal: 25,
    },
    xxlarge: {
      top: 40,
      bottom: 30,
      horizontal: 30,
    },
  };

  return safeArea[currentScreenSize] || safeArea.medium;
};
